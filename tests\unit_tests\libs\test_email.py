import pytest

from libs.helper import email


def test_email_with_valid_email():
    assert email("<EMAIL>") == "<EMAIL>"
    assert email("<EMAIL>") == "<EMAIL>"
    assert email("<EMAIL>") == "<EMAIL>"
    assert email("!#$%&'*+-/=?^_{|}~`@example.com") == "!#$%&'*+-/=?^_{|}~`@example.com"


def test_email_with_invalid_email():
    with pytest.raises(ValueError, match="invalid_email is not a valid email."):
        email("invalid_email")

    with pytest.raises(ValueError, match="@example.com is not a valid email."):
        email("@example.com")

    with pytest.raises(ValueError, match="()@example.com is not a valid email."):
        email("()@example.com")
