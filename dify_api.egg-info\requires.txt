arize-phoenix-otel~=0.9.2
authlib==1.3.1
azure-identity==1.16.1
beautifulsoup4==4.12.2
boto3==1.35.99
bs4~=0.0.1
cachetools~=5.3.0
celery~=5.5.2
chardet~=5.1.0
flask~=3.1.0
flask-compress~=1.17
flask-cors~=6.0.0
flask-login~=0.6.3
flask-migrate~=4.0.7
flask-restful~=0.3.10
flask-sqlalchemy~=3.1.1
gevent~=24.11.1
gmpy2~=2.2.1
google-api-core==2.18.0
google-api-python-client==2.90.0
google-auth==2.29.0
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.49.0
googleapis-common-protos==1.63.0
gunicorn~=23.0.0
httpx[socks]~=0.27.0
jieba==0.42.1
json-repair>=0.41.1
langfuse~=2.51.3
langsmith~=0.1.77
mailchimp-transactional~=1.0.50
markdown~=3.5.1
numpy~=1.26.4
openai~=1.61.0
openpyxl~=3.1.5
opik~=1.7.25
opentelemetry-api==1.27.0
opentelemetry-distro==0.48b0
opentelemetry-exporter-otlp==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-exporter-otlp-proto-http==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-celery==0.48b0
opentelemetry-instrumentation-flask==0.48b0
opentelemetry-instrumentation-sqlalchemy==0.48b0
opentelemetry-propagator-b3==1.27.0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
pandas[excel,output-formatting,performance]~=2.2.2
pandoc~=2.4
psycogreen~=1.0.2
psycopg2-binary~=2.9.6
pycryptodome==3.19.1
pydantic~=2.11.4
pydantic-extra-types~=2.10.3
pydantic-settings~=2.9.1
pyjwt~=2.8.0
pypdfium2==4.30.0
python-docx~=1.1.0
python-dotenv==1.0.1
pyyaml~=6.0.1
readabilipy~=0.3.0
redis[hiredis]~=6.1.0
resend~=2.9.0
sentry-sdk[flask]~=2.28.0
sqlalchemy~=2.0.29
starlette==0.41.0
tiktoken~=0.9.0
transformers~=4.51.0
unstructured[docx,epub,md,ppt,pptx]~=0.16.1
weave~=0.51.0
yarl~=1.18.3
webvtt-py~=0.5.1
sendgrid~=6.12.3
