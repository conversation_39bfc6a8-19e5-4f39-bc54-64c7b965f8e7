Metadata-Version: 2.4
Name: dify-api
Version: 1.5.1
Requires-Python: <3.13,>=3.11
Requires-Dist: arize-phoenix-otel~=0.9.2
Requires-Dist: authlib==1.3.1
Requires-Dist: azure-identity==1.16.1
Requires-Dist: beautifulsoup4==4.12.2
Requires-Dist: boto3==1.35.99
Requires-Dist: bs4~=0.0.1
Requires-Dist: cachetools~=5.3.0
Requires-Dist: celery~=5.5.2
Requires-Dist: chardet~=5.1.0
Requires-Dist: flask~=3.1.0
Requires-Dist: flask-compress~=1.17
Requires-Dist: flask-cors~=6.0.0
Requires-Dist: flask-login~=0.6.3
Requires-Dist: flask-migrate~=4.0.7
Requires-Dist: flask-restful~=0.3.10
Requires-Dist: flask-sqlalchemy~=3.1.1
Requires-Dist: gevent~=24.11.1
Requires-Dist: gmpy2~=2.2.1
Requires-Dist: google-api-core==2.18.0
Requires-Dist: google-api-python-client==2.90.0
Requires-Dist: google-auth==2.29.0
Requires-Dist: google-auth-httplib2==0.2.0
Requires-Dist: google-cloud-aiplatform==1.49.0
Requires-Dist: googleapis-common-protos==1.63.0
Requires-Dist: gunicorn~=23.0.0
Requires-Dist: httpx[socks]~=0.27.0
Requires-Dist: jieba==0.42.1
Requires-Dist: json-repair>=0.41.1
Requires-Dist: langfuse~=2.51.3
Requires-Dist: langsmith~=0.1.77
Requires-Dist: mailchimp-transactional~=1.0.50
Requires-Dist: markdown~=3.5.1
Requires-Dist: numpy~=1.26.4
Requires-Dist: openai~=1.61.0
Requires-Dist: openpyxl~=3.1.5
Requires-Dist: opik~=1.7.25
Requires-Dist: opentelemetry-api==1.27.0
Requires-Dist: opentelemetry-distro==0.48b0
Requires-Dist: opentelemetry-exporter-otlp==1.27.0
Requires-Dist: opentelemetry-exporter-otlp-proto-common==1.27.0
Requires-Dist: opentelemetry-exporter-otlp-proto-grpc==1.27.0
Requires-Dist: opentelemetry-exporter-otlp-proto-http==1.27.0
Requires-Dist: opentelemetry-instrumentation==0.48b0
Requires-Dist: opentelemetry-instrumentation-celery==0.48b0
Requires-Dist: opentelemetry-instrumentation-flask==0.48b0
Requires-Dist: opentelemetry-instrumentation-sqlalchemy==0.48b0
Requires-Dist: opentelemetry-propagator-b3==1.27.0
Requires-Dist: opentelemetry-proto==1.27.0
Requires-Dist: opentelemetry-sdk==1.27.0
Requires-Dist: opentelemetry-semantic-conventions==0.48b0
Requires-Dist: opentelemetry-util-http==0.48b0
Requires-Dist: pandas[excel,output-formatting,performance]~=2.2.2
Requires-Dist: pandoc~=2.4
Requires-Dist: psycogreen~=1.0.2
Requires-Dist: psycopg2-binary~=2.9.6
Requires-Dist: pycryptodome==3.19.1
Requires-Dist: pydantic~=2.11.4
Requires-Dist: pydantic-extra-types~=2.10.3
Requires-Dist: pydantic-settings~=2.9.1
Requires-Dist: pyjwt~=2.8.0
Requires-Dist: pypdfium2==4.30.0
Requires-Dist: python-docx~=1.1.0
Requires-Dist: python-dotenv==1.0.1
Requires-Dist: pyyaml~=6.0.1
Requires-Dist: readabilipy~=0.3.0
Requires-Dist: redis[hiredis]~=6.1.0
Requires-Dist: resend~=2.9.0
Requires-Dist: sentry-sdk[flask]~=2.28.0
Requires-Dist: sqlalchemy~=2.0.29
Requires-Dist: starlette==0.41.0
Requires-Dist: tiktoken~=0.9.0
Requires-Dist: transformers~=4.51.0
Requires-Dist: unstructured[docx,epub,md,ppt,pptx]~=0.16.1
Requires-Dist: weave~=0.51.0
Requires-Dist: yarl~=1.18.3
Requires-Dist: webvtt-py~=0.5.1
Requires-Dist: sendgrid~=6.12.3
